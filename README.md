# SignalR Chat Frontend

A modern, real-time chat application built with React, TypeScript, Vite, and Tailwind CSS, integrated with SignalR for real-time messaging.

## 🚀 Features

- **Real-time messaging** with SignalR
- **Beautiful Tailwind UI** design
- **TypeScript** for type safety
- **Automatic reconnection** handling
- **Connection status indicators**
- **Message history** with timestamps
- **Responsive design** for all devices
- **User avatars** with initials
- **Auto-scroll** to latest messages

## 🛠️ Prerequisites

Make sure you have the SignalR Chat API backend running:

- **Backend URL**: `http://localhost:5251`
- **SignalR Hub**: `http://localhost:5251/chatHub`
- **Swagger Docs**: `http://localhost:5251/swagger`

## 📦 Installation

1. **Install dependencies**:

```bash
npm install
```

2. **Configure environment** (optional):
   Create a `.env` file to customize the backend URLs:

```env
VITE_API_BASE_URL=http://localhost:5251
VITE_SIGNALR_HUB_URL=http://localhost:5251/chatHub
```

3. **Start the development server**:

```bash
npm run dev
```

4. **Open your browser** and navigate to `http://localhost:5173`

## 🎯 Usage

1. **Enter your name** in the user input field
2. **Wait for connection** - the status indicator will show "Connected" when ready
3. **Type your message** and click send or press Enter
4. **Chat in real-time** with other users!

## 🏗️ Project Structure

```
src/
├── App.tsx          # Main chat component
├── App.css          # Custom styles
├── index.css        # Tailwind CSS imports
├── main.tsx         # App entry point
└── assets/          # Static assets
```

## 🔧 Configuration

### Environment Variables

- `VITE_API_BASE_URL` - Backend API base URL (default: http://localhost:5251)
- `VITE_SIGNALR_HUB_URL` - SignalR hub URL (default: http://localhost:5251/chatHub)

### Tailwind CSS

The project uses Tailwind CSS for styling with a modern, professional design:

- Clean gray and white color scheme
- Indigo accent colors
- Responsive design patterns
- Smooth animations and transitions

## 🐛 Troubleshooting

### CORS Error (Most Common Issue)

If you see this error:

```
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at http://localhost:5251/chatHub/negotiate
```

**Solution**: Your backend needs to allow CORS from your frontend domain. Add this to your backend:

#### For ASP.NET Core (Program.cs or Startup.cs):

```csharp
// Add CORS services
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:5173")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Add SignalR
builder.Services.AddSignalR();

var app = builder.Build();

// Use CORS (MUST be before UseRouting and UseEndpoints)
app.UseCors("AllowReactApp");

// Map SignalR hub
app.MapHub<ChatHub>("/chatHub");
```

#### Alternative CORS Configuration (More Permissive - Development Only):

```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

app.UseCors("AllowAll");
```

### Other Connection Issues

If you see "Disconnected" status:

1. **Check backend** - Ensure the SignalR backend is running at `http://localhost:5251`
2. **Check CORS** - Backend should allow connections from `http://localhost:5173`
3. **Check console** - Look for SignalR connection errors in browser console
4. **Check firewall** - Ensure ports 5251 and 5173 are not blocked

### Common Errors

- **CORS Error**: Backend needs to allow your frontend origin (see above)
- **Connection Failed**: Backend might not be running or wrong URL
- **Messages not sending**: Check connection status and user name
- **Negotiation Failed**: Usually a CORS or backend configuration issue

## 🚀 Building for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## 📚 Technologies Used

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **@microsoft/signalr** - Real-time communication
- **@heroicons/react** - Beautiful icons
- **@headlessui/react** - Accessible UI components

## 🤝 Integration with Backend

This frontend is designed to work with the SignalR Chat API backend. Make sure your backend:

1. **Exposes SignalR hub** at `/chatHub`
2. **Implements required methods**:
   - `SendMessage(user, message)`
   - `GetMessages()`
3. **Sends events**:
   - `ReceiveMessage(user, message, timestamp)`
   - `LoadMessages(messages[])`
4. **Allows CORS** from your frontend domain

## 📄 License

This project is open source and available under the MIT License.
