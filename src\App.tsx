import React, { useState, useEffect, useRef } from 'react';
import * as signalR from '@microsoft/signalr';
import { PaperAirplaneIcon, UserIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

interface Message {
  user: string;
  message: string;
  timestamp: string;
}

const App: React.FC = () => {
  const [connection, setConnection] = useState<signalR.HubConnection | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [user, setUser] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [connectionError, setConnectionError] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Establish SignalR connection
  useEffect(() => {
    const hubUrl = import.meta.env.VITE_SIGNALR_HUB_URL || '/chatHub';
    const newConnection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        skipNegotiation: false,
        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.ServerSentEvents | signalR.HttpTransportType.LongPolling,
        accessTokenFactory: () => {
          // Add any authentication token here if needed
          return "";
        },
        headers: {
          // Add any custom headers if needed
        },
        withCredentials: false
      })
      .withAutomaticReconnect([0, 2000, 10000, 30000])
      .configureLogging(signalR.LogLevel.Information)
      .build();

    setConnection(newConnection);

    return () => {
      if (newConnection) {
        newConnection.stop();
      }
    };
  }, []);

  // Start connection and handle messages
  useEffect(() => {
    if (connection) {
      setIsConnecting(true);

      connection
        .start()
        .then(() => {
          console.log('Connected to SignalR');
          setIsConnected(true);
          setIsConnecting(false);
          setConnectionError('');
          connection.invoke('GetMessages'); // Load existing messages
        })
        .catch((err) => {
          console.error('SignalR Connection Error: ', err);
          setIsConnected(false);
          setIsConnecting(false);

          // Set user-friendly error message based on error type
          if (err.message.includes('CORS') || err.message.includes('NetworkError') || err.message.includes('Failed to complete negotiation')) {
            setConnectionError('CORS Error: Backend server needs to allow connections from this domain (http://localhost:5173)');
          } else if (err.message.includes('timeout') || err.message.includes('refused')) {
            setConnectionError('Connection Error: Backend server might not be running at http://localhost:5251');
          } else {
            setConnectionError(`Connection Error: ${err.message}`);
          }
        });

      connection.on('ReceiveMessage', (user: string, message: string, timestamp: string) => {
        setMessages((prevMessages) => [
          ...prevMessages,
          { user, message, timestamp: new Date(timestamp).toLocaleString() },
        ]);
      });

      connection.on('LoadMessages', (messages: any[]) => {
        setMessages(
          messages.map((msg) => ({
            user: msg.user,
            message: msg.content || msg.message,
            timestamp: new Date(msg.timestamp).toLocaleString(),
          }))
        );
      });

      connection.onreconnecting(() => {
        setIsConnected(false);
        setIsConnecting(true);
      });

      connection.onreconnected(() => {
        setIsConnected(true);
        setIsConnecting(false);
      });

      connection.onclose(() => {
        setIsConnected(false);
        setIsConnecting(false);
      });
    }
  }, [connection]);

  // Send message
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (connection && user && message && isConnected) {
      try {
        await connection.invoke('SendMessage', user, message);
        setMessage('');
      } catch (err) {
        console.error('Error sending message: ', err);
      }
    }
  };

  const getConnectionStatus = () => {
    if (isConnecting) return { text: 'Connecting...', color: 'text-yellow-500' };
    if (isConnected) return { text: 'Connected', color: 'text-green-500' };
    return { text: 'Disconnected', color: 'text-red-500' };
  };

  const retryConnection = async () => {
    if (connection && !isConnecting) {
      setConnectionError('');
      setIsConnecting(true);
      try {
        await connection.start();
        setIsConnected(true);
        setIsConnecting(false);
        await connection.invoke('GetMessages');
      } catch (err: any) {
        setIsConnected(false);
        setIsConnecting(false);
        if (err.message.includes('CORS') || err.message.includes('NetworkError') || err.message.includes('Failed to complete negotiation')) {
          setConnectionError('CORS Error: Backend server needs to allow connections from this domain (http://localhost:5173)');
        } else {
          setConnectionError(`Connection Error: ${err.message}`);
        }
      }
    }
  };

  const status = getConnectionStatus();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden">
          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-5">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <ChatBubbleLeftRightIcon className="h-9 w-9 text-white" />
                <h1 className="text-2xl font-bold text-white">SignalR Chat</h1>
              </div>
              <div className="flex items-center space-x-4">
                <div className={`flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
                  isConnected ? 'bg-emerald-100 text-emerald-800' :
                  isConnecting ? 'bg-amber-100 text-amber-800' : 'bg-rose-100 text-rose-800 animate-pulse'
                }`}>
                  <div className={`w-3 h-3 rounded-full mr-2 ${
                    isConnected ? 'bg-emerald-500' :
                    isConnecting ? 'bg-amber-500' : 'bg-rose-500'
                  }`}></div>
                  {status.text}
                </div>
                <div className="text-sm font-medium text-white bg-black bg-opacity-20 px-3 py-1 rounded-full">
                  Messages: {messages.length}
                </div>
              </div>
            </div>
          </div>

          {/* User Input */}
          <div className="px-6 py-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <UserIcon className="h-5 w-5 text-indigo-600" />
              </div>
              <input
                type="text"
                placeholder="Enter your name"
                value={user}
                onChange={(e) => setUser(e.target.value)}
                className="flex-1 block w-full rounded-lg border-0 py-3 px-4 bg-gray-100 text-gray-800 placeholder-gray-500 focus:ring-2 focus:ring-indigo-500 focus:bg-white transition-all duration-200"
              />
            </div>
          </div>
        </div>

        {/* Error Display */}
        {connectionError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Connection Failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{connectionError}</p>
                  {connectionError.includes('CORS') && (
                    <div className="mt-2 p-3 bg-red-100 rounded border">
                      <p className="font-medium">To fix this CORS error, your backend needs to:</p>
                      <ul className="mt-1 list-disc list-inside space-y-1">
                        <li>Allow origin: <code className="bg-red-200 px-1 rounded">http://localhost:5173</code></li>
                        <li>Enable CORS for SignalR in your startup configuration</li>
                        <li>Add proper CORS headers for preflight requests</li>
                      </ul>
                      <p className="mt-2 text-sm">
                        📄 See <code className="bg-red-200 px-1 rounded">CORS-FIX.md</code> for detailed instructions
                      </p>
                    </div>
                  )}
                  <div className="mt-3">
                    <button
                      onClick={retryConnection}
                      disabled={isConnecting}
                      className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isConnecting ? 'Retrying...' : 'Retry Connection'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chat Container */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col">
          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-6">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-indigo-100 mb-4">
                  <ChatBubbleLeftRightIcon className="h-10 w-10 text-indigo-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to the chat!</h3>
                <p className="text-gray-500 max-w-md mx-auto">Enter your name above and start a conversation. Your messages will appear here.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((msg, index) => (
<div
  key={index}
  className={`flex ${msg.user === user ? 'justify-end' : 'justify-start'} animate-message-in`}
>
                    <div 
                      className={`max-w-[85%] rounded-2xl px-4 py-3 ${
                        msg.user === user 
                          ? 'bg-indigo-500 text-white rounded-br-none' 
                          : 'bg-gray-100 text-gray-800 rounded-bl-none'
                      }`}
                    >
                      <div className="flex items-baseline justify-between space-x-3 mb-1">
                        <span className="font-medium text-sm">{msg.user}</span>
                        <span className={`text-xs ${msg.user === user ? 'text-indigo-200' : 'text-gray-500'}`}>
                          {msg.timestamp}
                        </span>
                      </div>
                      <p className="text-base">{msg.message}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div ref={messagesEndRef} className="h-4" />
          </div>

          {/* Message Input */}
          <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
            <form onSubmit={sendMessage} className="flex space-x-3 items-center">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Type your message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  disabled={!isConnected || !user}
                  className="block w-full rounded-lg border-0 py-3 px-4 bg-white text-gray-800 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-500 disabled:bg-gray-100 disabled:cursor-not-allowed transition-all duration-200"
                />
              </div>
<button
  type="submit"
  disabled={!isConnected || !user || !message.trim()}
  className="inline-flex items-center justify-center w-12 h-12 rounded-full text-white bg-indigo-500 hover:bg-indigo-600 hover:scale-110 hover:shadow-lg transform transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200"
>
  <PaperAirplaneIcon className="h-5 w-5" />
</button>
            </form>
            <div className="mt-2 flex justify-between">
              {!user && (
                <p className="text-xs text-rose-500">Please enter your name to start chatting</p>
              )}
              {!isConnected && (
                <p className="text-xs text-rose-500">
                  Not connected to chat server ({import.meta.env.VITE_SIGNALR_HUB_URL || '/chatHub (proxied to http://localhost:5251)'})
                </p>
              )}
              <div className="flex-1"></div>
              {message.trim() && (
                <p className="text-xs text-gray-500 text-right">
                  Press Enter or click send
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
